@tailwind base;
@tailwind components;
@tailwind utilities;

/* Micro SaaS Design System - Professional & Modern */

@layer base {
  :root {
    /* Core Colors - Professional Blue/Indigo Theme */
    --background: 240 10% 98%;
    --foreground: 240 10% 8%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 8%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 8%;

    /* Primary - Modern Blue */
    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 221 100% 65%;

    /* Secondary - Light Blue */
    --secondary: 220 14% 96%;
    --secondary-foreground: 220 9% 15%;

    /* Muted - Subtle Grays */
    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;

    /* Accent - Vibrant Blue */
    --accent: 221 83% 53%;
    --accent-foreground: 0 0% 98%;

    /* Success - Modern Green */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;

    /* Warning - Professional Orange */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 221 83% 53%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-card: linear-gradient(135deg, hsl(0 0% 100%), hsl(220 14% 98%));
    --gradient-background: linear-gradient(180deg, hsl(var(--background)), hsl(220 14% 96%));

    /* Shadows */
    --shadow-soft: 0 2px 8px -2px hsl(221 83% 53% / 0.1);
    --shadow-medium: 0 4px 16px -4px hsl(221 83% 53% / 0.15);
    --shadow-large: 0 8px 32px -8px hsl(221 83% 53% / 0.2);

    /* Transitions */
    --transition-smooth: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}